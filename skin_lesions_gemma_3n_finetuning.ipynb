{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔬 Gemma 3N 4B Vision Fine-tuning para Classificação de Lesões de Pele\n", "\n", "Este notebook adapta o fine-tuning do Gemma 3N 4B Vision para classificar 14 tipos de lesões de pele usando o dataset `ahmed-ai/skin-lesions-classification-dataset`.\n", "\n", "## 📊 Dataset: Skin Lesions Classification\n", "- **Total de amostras**: 36.656 imagens\n", "- **Classes**: 14 tipos de lesões de pele\n", "- **Splits**: train (29.3k), validation (3.66k), test (3.67k)\n", "- **Fonte**: Combinação de HAM10000 e MSLDv2.0\n", "\n", "### 🏥 Classes de Lesões:\n", "1. Actinic keratoses\n", "2. Basal cell carcinoma\n", "3. Benign keratosis-like-lesions\n", "4. Chicken<PERSON>\n", "5. <PERSON><PERSON><PERSON>\n", "6. <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "7. <PERSON><PERSON>\n", "8. HFMD\n", "9. <PERSON><PERSON><PERSON>\n", "10. Melanocytic nevi\n", "11. <PERSON><PERSON><PERSON>\n", "12. <PERSON><PERSON>\n", "13. Squamous cell carcinoma\n", "14. Vascular lesions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Seção 1: Instalação e Configuração"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "import os\n", "if \"COLAB_\" not in \"\".join(os.environ.keys()):\n", "    !pip install unsloth\n", "else:\n", "    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n", "    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo\n", "    !pip install sentencepiece protobuf \"datasets>=3.4.1,<4.0.0\" \"huggingface_hub>=0.34.0\" hf_transfer\n", "    !pip install --no-deps unsloth"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "# Install latest transformers for Gemma 3N\n", "!pip install --no-deps --upgrade transformers # Only for Gemma 3N\n", "!pip install --no-deps --upgrade timm # Only for Gemma 3N"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Seção 1.5: Correções Preventivas (Execute se houver problemas)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CORREÇÕES PREVENTIVAS - Execute esta célula se encontrar erros\n", "print(\"🔧 Aplicando correções preventivas...\")\n", "\n", "try:\n", "    # 1. Co<PERSON>gir problema do AcceleratorState\n", "    from accelerate.state import AcceleratorState\n", "    if hasattr(AcceleratorState, '_reset_state'):\n", "        AcceleratorState._reset_state()\n", "        print(\"✅ AcceleratorState resetado\")\n", "    \n", "    # 2. Limpar cache CUDA\n", "    import torch\n", "    if torch.cuda.is_available():\n", "        torch.cuda.empty_cache()\n", "        print(\"✅ Cache CUDA limpo\")\n", "    \n", "    # 3. Configurar variáveis de ambiente\n", "    import os\n", "    os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\"\n", "    os.environ[\"PYTORCH_CUDA_ALLOC_CONF\"] = \"max_split_size_mb:512\"\n", "    print(\"✅ Variáveis de ambiente configuradas\")\n", "    \n", "    # 4. Verificar versões críticas\n", "    import pkg_resources\n", "    critical_packages = ['unsloth', 'transformers', 'accelerate', 'torch']\n", "    \n", "    print(\"\\n📦 Versões dos pacotes críticos:\")\n", "    for package in critical_packages:\n", "        try:\n", "            version = pkg_resources.get_distribution(package).version\n", "            print(f\"  - {package}: {version}\")\n", "        except:\n", "            print(f\"  - {package}: Não encontrado\")\n", "    \n", "    print(\"\\n✅ Correções preventivas aplicadas\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️ Algumas correções falharam: {e}\")\n", "    print(\"💡 Continue com o notebook normalmente\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ⚙️ Seção 2: Configurações de Sistema e Otimizações de RAM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configurações para otimização de RAM (máximo 28GB disponível)\n", "import os\n", "import gc\n", "import warnings\n", "import torch\n", "\n", "# Configurações de sistema para otimizar RAM\n", "os.environ[\"PYTORCH_CUDA_ALLOC_CONF\"] = \"max_split_size_mb:512,expandable_segments:True\"\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Função para limpeza de memória\n", "def cleanup_memory():\n", "    gc.collect()\n", "    if torch.cuda.is_available():\n", "        torch.cuda.empty_cache()\n", "        torch.cuda.synchronize()\n", "\n", "print(\"✅ Configurações de sistema aplicadas\")\n", "cleanup_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📚 Seção 3: Carregamento do Modelo e Tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unsloth import FastVisionModel\n", "import torch\n", "\n", "# Configurações do modelo\n", "model_name = \"unsloth/gemma-3n-E4B-it\"  # Usando versão instruction-tuned\n", "max_seq_length = 2048\n", "dtype = None  # Auto detection\n", "load_in_4bit = True  # Usar 4-bit para economizar RAM\n", "\n", "print(f\"🔄 Carregando modelo: {model_name}\")\n", "\n", "model, tokenizer = FastVisionModel.from_pretrained(\n", "    model_name = model_name,\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", ")\n", "\n", "print(\"✅ Modelo carregado com sucesso!\")\n", "cleanup_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Seção 4: Configuração do LoRA para Fine-tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CORREÇÃO: Configurar LoRA para ter ~76.8M parâmetros treináveis (0.97%)\n", "model = FastVisionModel.get_peft_model(\n", "    model,\n", "    finetune_vision_layers = True,  # Fine-tune vision layers também\n", "    finetune_language_layers = True,\n", "    finetune_attention_modules = True,\n", "    finetune_mlp_modules = True,\n", "    r = 32,  # CORRIGIDO: era 16, agora 32 para mais parâmetros\n", "    lora_alpha = 32,  # CORRIGIDO: era 16, agora 32\n", "    lora_dropout = 0.05,\n", "    bias = \"none\",\n", "    random_state = 3407,\n", "    use_rslora = False,\n", "    loftq_config = None,\n", "    # Adicionar mais módulos para aumentar parâmetros treináveis\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\", \"gate_proj\", \"up_proj\", \"down_proj\"],\n", ")\n", "\n", "trainable_params = model.get_nb_trainable_parameters()\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_percentage = (trainable_params / total_params) * 100\n", "\n", "print(\"✅ Configuração LoRA corrigida\")\n", "print(f\"📊 Parâmetros treináveis: {trainable_params:,} de {total_params:,} ({trainable_percentage:.2f}%)\")\n", "print(f\"🎯 Esperado: ~76.8M (0.97%) - Atual: {trainable_params/1e6:.1f}M ({trainable_percentage:.2f}%)\")\n", "\n", "if trainable_percentage < 0.9:\n", "    print(\"⚠️  Aviso: Menos parâmetros treináveis que o esperado\")\n", "    print(\"💡 Isso pode afetar a performance, mas ainda deve funcionar\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Seção 5: Carregamento e Análise do Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset\n", "import pandas as pd\n", "from collections import Counter\n", "\n", "print(\"🔄 Carregando dataset de lesões de pele...\")\n", "\n", "# Carregar dataset\n", "dataset = load_dataset(\"ahmed-ai/skin-lesions-classification-dataset\")\n", "\n", "print(f\"📊 Dataset carregado:\")\n", "print(f\"  - Train: {len(dataset['train'])} amostras\")\n", "print(f\"  - Validation: {len(dataset['validation'])} amostras\")\n", "print(f\"  - Test: {len(dataset['test'])} amostras\")\n", "\n", "# Analisar distribuição das classes\n", "train_labels = dataset['train']['label']\n", "label_counts = Counter(train_labels)\n", "\n", "print(\"\\n🏥 Distribuição das classes no conjunto de treino:\")\n", "for label_id, count in sorted(label_counts.items()):\n", "    print(f\"  Classe {label_id}: {count} amostras\")\n", "\n", "cleanup_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Seção 6: Mapeamento de Classes e Preparação dos Dados"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapeamento das classes de lesões de pele\n", "CLASS_NAMES = {\n", "    0: \"Actinic keratoses\",\n", "    1: \"Basal cell carcinoma\", \n", "    2: \"Benign keratosis-like-lesions\",\n", "    3: \"Chickenpox\",\n", "    4: \"Cowpox\",\n", "    5: \"Dermatofibroma\",\n", "    6: \"Healthy\",\n", "    7: \"HFMD\",\n", "    8: \"Measles\",\n", "    9: \"Melanocytic nevi\",\n", "    10: \"Melanoma\",\n", "    11: \"Monkeypox\",\n", "    12: \"Squamous cell carcinoma\",\n", "    13: \"Vascular lesions\"\n", "}\n", "\n", "print(\"🏥 Classes de lesões de pele mapeadas:\")\n", "for class_id, class_name in CLASS_NAMES.items():\n", "    count = label_counts.get(class_id, 0)\n", "    print(f\"  {class_id}: {class_name} ({count} amostras)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎨 Seção 7: Função de Formatação de Dados com Otimização de RAM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_skin_lesion_data(examples):\n", "    \"\"\"\n", "    Formatar dados para classificação de lesões de pele\n", "    Otimizado para uso eficiente de RAM\n", "    \"\"\"\n", "    images = examples[\"image\"]\n", "    labels = examples[\"label\"]\n", "    \n", "    texts = []\n", "    for label in labels:\n", "        class_name = CLASS_NAMES[label]\n", "        \n", "        # Prompt estruturado para classificação médica\n", "        prompt = f\"\"\"<|im_start|>user\n", "Analyze this dermatological image and classify the skin lesion type. \n", "Provide a medical diagnosis based on the visual characteristics.\n", "\n", "Image: <image>\n", "\n", "What type of skin lesion is shown in this image?<|im_end|>\n", "<|im_start|>assistant\n", "Based on the dermatological analysis of this image, this appears to be: {class_name}\n", "\n", "This diagnosis is based on the characteristic visual features and patterns observed in the lesion.<|im_end|>\"\"\"\n", "        \n", "        texts.append(prompt)\n", "    \n", "    return {\n", "        \"image\": images,  # Manter nome original para compatibilidade\n", "        \"text\": texts,    # No<PERSON> padrão esperado pelo SFTTrainer\n", "        \"label\": labels,  # Manter labels originais\n", "    }\n", "\n", "print(\"✅ Função de formatação definida\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Seção 8: Processamento do Dataset com Otimização de Memória"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CORREÇÃO: Usar dataset completo conforme esperado\n", "print(\"🔄 Preparando dataset completo...\")\n", "\n", "# Usar dataset de treino completo (não apenas 10k)\n", "train_subset = dataset['train']  # Dataset completo: ~29k amostras\n", "\n", "print(f\"📊 Usando {len(train_subset)} amostras para treinamento (dataset completo)\")\n", "\n", "# Verificar se temos todas as amostras esperadas\n", "expected_samples = 68686  # Valor esperado do seu exemplo\n", "if len(train_subset) < expected_samples:\n", "    print(f\"⚠️  Aviso: <PERSON><PERSON> {len(train_subset)} amostras, esperado {expected_samples}\")\n", "    print(\"💡 Isso pode ser normal se o dataset foi atualizado\")\n", "\n", "# Processar dataset de forma mais direta\n", "print(\"🔄 Processando dataset...\")\n", "\n", "# Aplicar formatação ao dataset\n", "train_dataset = train_subset.map(\n", "    format_skin_lesion_data,\n", "    batched=True,\n", "    batch_size=100,  # Lotes pequenos para economizar RAM\n", "    remove_columns=[\"image\", \"label\"],  # Remover colunas originais\n", "    desc=\"Formatando dados\"\n", ")\n", "\n", "print(f\"✅ Dataset processado: {len(train_dataset)} amostras\")\n", "cleanup_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🏋️ Seção 9: Configuração do Treinamento"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from trl import SFTTrainer\n", "from transformers import TrainingArguments, DataCollatorForSeq2Seq\n", "from unsloth import is_bfloat16_supported\n", "\n", "# CORREÇÃO: Configurações para corresponder ao exemplo esperado\n", "training_args = TrainingArguments(\n", "    per_device_train_batch_size = 2,  # CORRIGIDO: era 1, agora 2\n", "    gradient_accumulation_steps = 4,  # CORRIGIDO: era 8, agora 4\n", "    warmup_steps = 5,  # CORRIGIDO: reduzido para ter ~60 steps totais\n", "    num_train_epochs = 1,  # Manter 1 época\n", "    max_steps = 60,  # CORRIGIDO: era 500, agora 60 conforme esperado\n", "    learning_rate = 2e-4,\n", "    fp16 = not is_bfloat16_supported(),\n", "    bf16 = is_bfloat16_supported(),\n", "    logging_steps = 5,  # CORRIGIDO: mais frequente para 60 steps\n", "    optim = \"adamw_8bit\",  # Manter otimizador 8-bit\n", "    weight_decay = 0.01,\n", "    lr_scheduler_type = \"linear\",\n", "    seed = 3407,\n", "    output_dir = \"outputs\",\n", "    save_steps = 30,  # CORRIGIDO: ajustado para 60 steps\n", "    save_total_limit = 2,\n", "    dataloader_num_workers = 0,\n", "    remove_unused_columns = False,\n", "    group_by_length = True,\n", "    report_to = None,\n", "    gradient_checkpointing = True,  # ADICIONADO: para economizar VRAM\n", "    use_cache = False,  # ADICIONADO: compatível com gradient checkpointing\n", ")\n", "\n", "print(\"✅ Configurações de treinamento corrigidas\")\n", "print(f\"📊 Batch size efetivo: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps} (esperado: 8)\")\n", "print(f\"📈 Total steps: {training_args.max_steps} (esperado: ~60)\")\n", "print(f\"🔄 Gradient checkpointing: {training_args.gradient_checkpointing} (para economizar VRAM)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🔍 Seção 9.5: Verificação das Configurações Esperadas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verificar se as configurações correspondem ao exemplo esperado\n", "print(\"🔍 Verificando configurações contra o exemplo esperado...\")\n", "\n", "# Configurações esperadas do seu exemplo\n", "expected_config = {\n", "    \"num_examples\": 68686,\n", "    \"total_steps\": 60,\n", "    \"batch_size_per_device\": 2,\n", "    \"gradient_accumulation_steps\": 4,\n", "    \"total_batch_size\": 8,\n", "    \"trainable_params_millions\": 76.8,\n", "    \"trainable_percentage\": 0.97\n", "}\n", "\n", "# Configurações atuais\n", "current_config = {\n", "    \"num_examples\": len(train_dataset) if 'train_dataset' in locals() else len(train_subset),\n", "    \"total_steps\": training_args.max_steps,\n", "    \"batch_size_per_device\": training_args.per_device_train_batch_size,\n", "    \"gradient_accumulation_steps\": training_args.gradient_accumulation_steps,\n", "    \"total_batch_size\": training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps,\n", "}\n", "\n", "print(\"\\n📊 Comparação de Configurações:\")\n", "print(\"=\" * 60)\n", "print(f\"{'Parâmetro':<25} {'Esperado':<15} {'Atual':<15} {'Status':<10}\")\n", "print(\"=\" * 60)\n", "\n", "for key in [\"num_examples\", \"total_steps\", \"batch_size_per_device\", \"gradient_accumulation_steps\", \"total_batch_size\"]:\n", "    expected = expected_config[key]\n", "    current = current_config[key]\n", "    status = \"✅\" if current == expected else \"⚠️\"\n", "    print(f\"{key:<25} {expected:<15} {current:<15} {status:<10}\")\n", "\n", "print(\"=\" * 60)\n", "\n", "# Verificar parâmetros treináveis se o modelo estiver carregado\n", "if 'model' in locals():\n", "    try:\n", "        trainable_params = model.get_nb_trainable_parameters()\n", "        total_params = sum(p.numel() for p in model.parameters())\n", "        trainable_percentage = (trainable_params / total_params) * 100\n", "        trainable_millions = trainable_params / 1e6\n", "        \n", "        print(f\"\\n🧠 Parâmetros do Modelo:\")\n", "        print(f\"  Treináveis: {trainable_millions:.1f}M (esperado: {expected_config['trainable_params_millions']}M)\")\n", "        print(f\"  Percentual: {trainable_percentage:.2f}% (esperado: {expected_config['trainable_percentage']}%)\")\n", "        \n", "        if abs(trainable_millions - expected_config['trainable_params_millions']) > 10:\n", "            print(\"⚠️  Diferença significativa nos parâmetros treináveis\")\n", "        else:\n", "            print(\"✅ Parâmetros treináveis dentro do esperado\")\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️  Não foi possível verificar parâmetros do modelo: {e}\")\n", "\n", "print(\"\\n💡 Notas:\")\n", "print(\"  - Diferenças no num_examples podem ser normais (dataset atualizado)\")\n", "print(\"  - O importante é manter batch_size e gradient_accumulation corretos\")\n", "print(\"  - Gradient checkpointing deve estar ativo para economizar VRAM\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Seção 10: Inicialização do Trainer e Treinamento"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"📊 Dataset pronto para treinamento: {len(train_dataset)} amostras\")\n", "cleanup_memory()\n", "\n", "# Função de formatação para o SFTTrainer\n", "def formatting_prompts_func(examples):\n", "    \"\"\"\n", "    Função de formatação necessária para o SFTTrainer\n", "    Retorna os textos já formatados\n", "    \"\"\"\n", "    # Se temos o campo 'text' j<PERSON> processado, usar ele\n", "    if \"text\" in examples:\n", "        return examples[\"text\"]\n", "    \n", "    # Fallback para compatibilidade\n", "    return [\"Texto não encontrado\"] * len(examples.get(\"image\", []))\n", "\n", "# Inicializar trainer com formatting_func\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = train_dataset,\n", "    formatting_func = formatting_prompts_func,\n", "    args = training_args,\n", "    max_seq_length = max_seq_length,\n", "    data_collator = DataCollatorForSeq2Seq(tokenizer=tokenizer, padding=True),\n", ")\n", "\n", "print(\"✅ Trainer inicializado com formatting_func\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🔧 Alternativa: Abordagem Robusta para SFTTrainer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ALTERNATIVA: Se o trainer acima falhar, use esta abordagem mais robusta\n", "\n", "# Função de formatação mais robusta\n", "def robust_formatting_func(example):\n", "    \"\"\"\n", "    Função de formatação robusta que funciona com exemplos individuais\n", "    \"\"\"\n", "    # Se já temos o texto formatado, retornar ele\n", "    if \"text\" in example and example[\"text\"]:\n", "        return example[\"text\"]\n", "    \n", "    # Se temos label, criar o prompt\n", "    if \"label\" in example:\n", "        class_name = CLASS_NAMES[example[\"label\"]]\n", "        \n", "        prompt = f\"\"\"<|im_start|>user\n", "Analyze this dermatological image and classify the skin lesion type. \n", "Provide a medical diagnosis based on the visual characteristics.\n", "\n", "Image: <image>\n", "\n", "What type of skin lesion is shown in this image?<|im_end|>\n", "<|im_start|>assistant\n", "Based on the dermatological analysis of this image, this appears to be: {class_name}\n", "\n", "This diagnosis is based on the characteristic visual features and patterns observed in the lesion.<|im_end|>\"\"\"\n", "        \n", "        return prompt\n", "    \n", "    return \"Error: No valid data found\"\n", "\n", "# <PERSON><PERSON> inicializar trainer alternativo\n", "try:\n", "    print(\"🔄 Tentando abordagem alternativa para o trainer...\")\n", "    \n", "    # Usar dataset original com formatting_func robusta\n", "    trainer_alt = SFTTrainer(\n", "        model = model,\n", "        tokenizer = tokenizer,\n", "        train_dataset = train_subset,  # Usar dataset original\n", "        formatting_func = robust_formatting_func,\n", "        args = training_args,\n", "        max_seq_length = max_seq_length,\n", "        packing = False,  # Desabilitar packing para evitar problemas\n", "    )\n", "    \n", "    print(\"✅ Trainer alternativo inicializado com sucesso!\")\n", "    print(\"💡 Use 'trainer_alt' para treinamento se o trainer principal falhar\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️  Trainer alternativo também falhou: {e}\")\n", "    print(\"💡 Verifique se todas as dependências estão instaladas corretamente\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🔍 Debug: Verificação do Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Debug: Verificar estrutura do dataset\n", "print(\"🔍 Verificando estrutura do dataset...\")\n", "\n", "print(f\"\\n📊 Dataset original:\")\n", "print(f\"  - Colunas: {train_subset.column_names}\")\n", "print(f\"  - <PERSON><PERSON><PERSON>: {len(train_subset)}\")\n", "\n", "if 'train_dataset' in locals():\n", "    print(f\"\\n📊 Dataset processado:\")\n", "    print(f\"  - Colunas: {train_dataset.column_names}\")\n", "    print(f\"  - <PERSON><PERSON><PERSON>: {len(train_dataset)}\")\n", "    \n", "    # Mostrar exemplo\n", "    if len(train_dataset) > 0:\n", "        example = train_dataset[0]\n", "        print(f\"\\n📝 Exemplo do dataset processado:\")\n", "        for key, value in example.items():\n", "            if isinstance(value, str):\n", "                print(f\"  - {key}: {value[:100]}...\" if len(value) > 100 else f\"  - {key}: {value}\")\n", "            else:\n", "                print(f\"  - {key}: {type(value)}\")\n", "\n", "# Verificar exemplo original\n", "if len(train_subset) > 0:\n", "    original_example = train_subset[0]\n", "    print(f\"\\n📝 Exemplo do dataset original:\")\n", "    for key, value in original_example.items():\n", "        if key == 'image':\n", "            print(f\"  - {key}: {type(value)} (tamanho: {value.size if hasattr(value, 'size') else 'N/A'})\")\n", "        else:\n", "            print(f\"  - {key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Correção para problema do AcceleratorState\n", "print(\"🔧 Corrigindo problema do AcceleratorState...\")\n", "\n", "try:\n", "    # Resetar e reinicializar o Accelerator\n", "    from accelerate import Accelerator\n", "    from accelerate.state import AcceleratorState\n", "    \n", "    # Resetar estado se necessário\n", "    if hasattr(AcceleratorState, '_reset_state'):\n", "        AcceleratorState._reset_state()\n", "    \n", "    # Reinicializar Accelerator\n", "    accelerator = Accelerator()\n", "    \n", "    print(\"✅ AcceleratorState corrigido\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️  Aviso: {e}\")\n", "    print(\"💡 Continuando sem correção do Accelerator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reinicializar trainer com configurações corrigidas\n", "print(\"🔄 Reinicializando trainer...\")\n", "\n", "try:\n", "    # Limpar trainer anterior\n", "    if 'trainer' in locals():\n", "        del <PERSON>\n", "    \n", "    cleanup_memory()\n", "    \n", "    # Criar novo trainer\n", "    trainer = SFTT<PERSON>er(\n", "        model = model,\n", "        tokenizer = tokenizer,\n", "        train_dataset = train_dataset,\n", "        formatting_func = formatting_prompts_func,\n", "        args = training_args,\n", "        max_seq_length = max_seq_length,\n", "        data_collator = DataCollatorForSeq2Seq(tokenizer=tokenizer, padding=True),\n", "    )\n", "    \n", "    print(\"✅ Trainer reinicializado com sucesso\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Erro ao reinicializar trainer: {e}\")\n", "    print(\"💡 Tentando usar trainer alternativo...\")\n", "    \n", "    # Usar trainer alternativo se disponível\n", "    if 'trainer_alt' in locals():\n", "        trainer = trainer_alt\n", "        print(\"✅ Usando trainer alternativo\")\n", "    else:\n", "        print(\"❌ Trainer alternativo não disponível\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Iniciar treinamento com tratamento de erros\n", "print(\"🚀 Iniciando treinamento...\")\n", "print(\"⚠️  Este processo pode levar várias horas dependendo do hardware\")\n", "\n", "try:\n", "    # Limpeza final de memória antes do treinamento\n", "    cleanup_memory()\n", "    \n", "    # Treinar modelo\n", "    trainer_stats = trainer.train()\n", "    \n", "    print(\"✅ Treinamento concluído!\")\n", "    print(f\"📊 Estatísticas do treinamento: {trainer_stats}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Erro durante treinamento: {e}\")\n", "    print(\"\\n🔧 Tentativas de correção:\")\n", "    \n", "    # Tentar abordagem alternativa\n", "    try:\n", "        print(\"1️⃣ Tentando reinicializar ambiente...\")\n", "        \n", "        # Reinicializar completamente\n", "        import importlib\n", "        import sys\n", "        \n", "        # Recarregar módulos cr<PERSON>os\n", "        if 'accelerate' in sys.modules:\n", "            importlib.reload(sys.modules['accelerate'])\n", "        \n", "        # Tentar treinamento novamente\n", "        trainer_stats = trainer.train()\n", "        print(\"✅ Treinamento concluído após correção!\")\n", "        \n", "    except Exception as e2:\n", "        print(f\"2️⃣ Ainda com erro: {e2}\")\n", "        print(\"\\n💡 Soluções sugeridas:\")\n", "        print(\"   - Reiniciar o kernel do notebook\")\n", "        print(\"   - Executar: !pip install --upgrade accelerate\")\n", "        print(\"   - Verificar compatibilidade das versões\")\n", "        print(\"   - Usar um batch_size menor\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🛠️ Abordagem Alternativa: Treinamento Simplificado"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ABORDAGEM ALTERNATIVA: Treinamento mais simples sem Accelerator complexo\n", "print(\"🛠️ Tentando abordagem de treinamento simplificada...\")\n", "\n", "try:\n", "    # Configurações mais simples\n", "    simple_training_args = TrainingArguments(\n", "        output_dir=\"./simple_outputs\",\n", "        per_device_train_batch_size=1,\n", "        gradient_accumulation_steps=4,\n", "        num_train_epochs=1,\n", "        max_steps=100,  # Reduzido para teste\n", "        learning_rate=2e-4,\n", "        logging_steps=10,\n", "        save_steps=50,\n", "        warmup_steps=10,\n", "        fp16=True,\n", "        optim=\"adamw_torch\",  # Otimizador mais simples\n", "        dataloader_num_workers=0,\n", "        remove_unused_columns=False,\n", "        report_to=None,\n", "        save_total_limit=1,\n", "    )\n", "    \n", "    # Função de formatação ultra-simples\n", "    def simple_formatting_func(example):\n", "        if \"text\" in example:\n", "            return example[\"text\"]\n", "        elif \"label\" in example:\n", "            class_name = CLASS_NAMES.get(example[\"label\"], \"Unknown\")\n", "            return f\"This is a {class_name} skin lesion.\"\n", "        else:\n", "            return \"Medical image analysis.\"\n", "    \n", "    # Criar trainer simples\n", "    simple_trainer = SFTTrainer(\n", "        model=model,\n", "        tokenizer=tokenizer,\n", "        train_dataset=train_subset.select(range(100)),  # Apenas 100 amostras para teste\n", "        formatting_func=simple_formatting_func,\n", "        args=simple_training_args,\n", "        max_seq_length=512,  # Sequência mais curta\n", "        packing=False,\n", "    )\n", "    \n", "    print(\"✅ Trainer simples criado\")\n", "    print(\"🚀 Iniciando treinamento simplificado...\")\n", "    \n", "    # Treinar\n", "    simple_stats = simple_trainer.train()\n", "    \n", "    print(\"✅ Treinamento simplificado concluído!\")\n", "    print(f\"📊 Estatísticas: {simple_stats}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Treinamento simplificado também falhou: {e}\")\n", "    print(\"\\n🔄 Última tentativa: Treinamento manual básico...\")\n", "    \n", "    try:\n", "        # Treinamento manual muito básico\n", "        print(\"🔧 Configurando treinamento manual...\")\n", "        \n", "        # Preparar um batch simples\n", "        sample_data = train_subset.select(range(10))\n", "        \n", "        # Processar manualmente\n", "        for i, example in enumerate(sample_data):\n", "            if i >= 5:  # Apenas 5 exemplos para teste\n", "                break\n", "                \n", "            class_name = CLASS_NAMES[example['label']]\n", "            prompt = f\"This medical image shows: {class_name}\"\n", "            \n", "            # Tokenizar\n", "            inputs = tokenizer(\n", "                prompt,\n", "                return_tensors=\"pt\",\n", "                max_length=256,\n", "                truncation=True,\n", "                padding=True\n", "            )\n", "            \n", "            print(f\"  ✅ Processado exemplo {i+1}: {class_name}\")\n", "        \n", "        print(\"✅ Processamento manual concluído\")\n", "        print(\"💡 O modelo está pronto, mas o treinamento automático falhou\")\n", "        print(\"   Considere usar uma versão mais simples do código ou reiniciar o kernel\")\n", "        \n", "    except Exception as e3:\n", "        print(f\"❌ Todas as tentativas falharam: {e3}\")\n", "        print(\"\\n🆘 SOLUÇÃO RECOMENDADA:\")\n", "        print(\"1. <PERSON><PERSON><PERSON>r o kernel do notebook\")\n", "        print(\"2. <PERSON><PERSON><PERSON><PERSON> apenas as seções essenciais\")\n", "        print(\"3. Usar batch_size=1 e max_steps=10 para teste\")\n", "        print(\"4. Verificar versões: !pip list | grep -E 'unsloth|transformers|accelerate'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Seção 11: Salvamento do Modelo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Salvar modelo fine-tuned\n", "model_save_path = \"gemma-3n-skin-lesions-classifier\"\n", "\n", "print(f\"💾 Salvando modelo em: {model_save_path}\")\n", "\n", "# Salvar modelo e tokenizer\n", "model.save_pretrained(model_save_path)\n", "tokenizer.save_pretrained(model_save_path)\n", "\n", "print(\"✅ Modelo salvo com sucesso!\")\n", "\n", "# Salvar também em formato GGUF para inferência otimizada\n", "print(\"🔄 Salvando em formato GGUF...\")\n", "model.save_pretrained_gguf(model_save_path, tokenizer, quantization_method=\"q4_k_m\")\n", "print(\"✅ Modelo GGUF salvo!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 Seção 12: <PERSON><PERSON> do Modelo com Upload de Imagem"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Função para testar o modelo com uma imagem\n", "from PIL import Image\n", "import requests\n", "from io import BytesIO\n", "\n", "def test_skin_lesion_classification(image_input, model, tokenizer):\n", "    \"\"\"\n", "    Testar classificação de lesão de pele\n", "    \n", "    Args:\n", "        image_input: PIL Image ou caminho para imagem\n", "        model: Modelo fine-tuned\n", "        tokenizer: Tokenizer\n", "    \"\"\"\n", "    # Preparar imagem\n", "    if isinstance(image_input, str):\n", "        if image_input.startswith('http'):\n", "            response = requests.get(image_input)\n", "            image = Image.open(BytesIO(response.content))\n", "        else:\n", "            image = Image.open(image_input)\n", "    else:\n", "        image = image_input\n", "    \n", "    # Prompt para classificação\n", "    prompt = \"\"\"<|im_start|>user\n", "Analyze this dermatological image and classify the skin lesion type. \n", "Provide a medical diagnosis based on the visual characteristics.\n", "\n", "Image: <image>\n", "\n", "What type of skin lesion is shown in this image?<|im_end|>\n", "<|im_start|>assistant\"\"\"\n", "    \n", "    # Tokenizar\n", "    inputs = tokenizer(\n", "        prompt,\n", "        images=image,\n", "        return_tensors=\"pt\"\n", "    ).to(model.device)\n", "    \n", "    # G<PERSON><PERSON> resposta\n", "    with torch.no_grad():\n", "        outputs = model.generate(\n", "            **inputs,\n", "            max_new_tokens=150,\n", "            temperature=0.1,\n", "            do_sample=True,\n", "            pad_token_id=tokenizer.eos_token_id\n", "        )\n", "    \n", "    # Decodificar resposta\n", "    response = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "    \n", "    # Extrair apenas a resposta do assistente\n", "    assistant_response = response.split(\"<|im_start|>assistant\")[-1].strip()\n", "    \n", "    return assistant_response\n", "\n", "print(\"✅ Função de teste definida\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Testar com uma imagem do dataset de validação\n", "print(\"🧪 Testando modelo com imagem do dataset de validação...\")\n", "\n", "# Pegar uma imagem aleatória do conjunto de validação\n", "import random\n", "test_idx = random.randint(0, len(dataset['validation']) - 1)\n", "test_image = dataset['validation'][test_idx]['image']\n", "true_label = dataset['validation'][test_idx]['label']\n", "true_class = CLASS_NAMES[true_label]\n", "\n", "print(f\"📊 Imagem de teste - Classe verdadeira: {true_class}\")\n", "\n", "# Fazer predição\n", "try:\n", "    prediction = test_skin_lesion_classification(test_image, model, tokenizer)\n", "    print(f\"🤖 Predição do modelo: {prediction}\")\n", "    \n", "    # Mostrar imagem (se poss<PERSON>vel)\n", "    test_image.show() if hasattr(test_image, 'show') else None\n", "    \nexcept Exception as e:\n", "    print(f\"❌ Erro durante teste: {e}\")\n", "    print(\"💡 Isso pode ser normal se o modelo ainda está sendo carregado\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📤 Seção 13: Interface para Upload de Imagem"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interface simples para upload de imagem\n", "from IPython.display import display, HTML\n", "import ipywidgets as widgets\n", "from ipywidgets import FileUpload, Button, Output, VBox\n", "\n", "# Criar widgets para upload\n", "upload_widget = FileUpload(\n", "    accept='.png,.jpg,.jpeg',\n", "    multiple=False,\n", "    description='Upload Image'\n", ")\n", "\n", "classify_button = Button(\n", "    description='Classify Lesion',\n", "    button_style='success'\n", ")\n", "\n", "output_widget = Output()\n", "\n", "def on_classify_click(b):\n", "    with output_widget:\n", "        output_widget.clear_output()\n", "        \n", "        if not upload_widget.value:\n", "            print(\"❌ Por favor, faça upload de uma imagem primeiro\")\n", "            return\n", "        \n", "        try:\n", "            # Obter imagem do upload\n", "            uploaded_file = list(upload_widget.value.values())[0]\n", "            image = Image.open(BytesIO(uploaded_file['content']))\n", "            \n", "            print(\"🔄 Classificando lesão de pele...\")\n", "            \n", "            # Fazer predição\n", "            prediction = test_skin_lesion_classification(image, model, tokenizer)\n", "            \n", "            print(\"\\n🏥 RESULTADO DA ANÁLISE DERMATOLÓGICA:\")\n", "            print(\"=\" * 50)\n", "            print(prediction)\n", "            print(\"=\" * 50)\n", "            print(\"\\n⚠️  AVISO: Esta é uma análise automatizada para fins educacionais.\")\n", "            print(\"   Sempre consulte um dermatologista para diagnóstico médico real.\")\n", "            \n", "            # Mostrar imagem\n", "            display(image.resize((300, 300)))\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Erro durante classificação: {e}\")\n", "\n", "classify_button.on_click(on_classify_click)\n", "\n", "# Exibir interface\n", "interface = VBox([upload_widget, classify_button, output_widget])\n", "display(interface)\n", "\n", "print(\"✅ Interface de upload criada!\")\n", "print(\"📤 Faça upload de uma imagem de lesão de pele para testar o modelo\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Seção 14: Métricas e Avaliação Final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Avaliar modelo no conjunto de teste\n", "print(\"📊 Avaliando modelo no conjunto de teste...\")\n", "\n", "# Função para avaliar acurácia\n", "def evaluate_model_accuracy(model, tokenizer, test_dataset, num_samples=100):\n", "    \"\"\"\n", "    Avaliar acurácia do modelo em uma amostra do conjunto de teste\n", "    \"\"\"\n", "    correct_predictions = 0\n", "    total_predictions = 0\n", "    \n", "    # Selecionar amostra aleatória\n", "    test_indices = random.sample(range(len(test_dataset)), min(num_samples, len(test_dataset)))\n", "    \n", "    for i, idx in enumerate(test_indices):\n", "        try:\n", "            image = test_dataset[idx]['image']\n", "            true_label = test_dataset[idx]['label']\n", "            true_class = CLASS_NAMES[true_label]\n", "            \n", "            # Fazer predição\n", "            prediction = test_skin_lesion_classification(image, model, tokenizer)\n", "            \n", "            # Verificar se a classe verdadeira está na predição\n", "            if true_class.lower() in prediction.lower():\n", "                correct_predictions += 1\n", "            \n", "            total_predictions += 1\n", "            \n", "            if (i + 1) % 10 == 0:\n", "                print(f\"  Avaliado {i + 1}/{len(test_indices)} amostras\")\n", "                \n", "        except Exception as e:\n", "            print(f\"  ⚠️  Erro na amostra {idx}: {e}\")\n", "            continue\n", "    \n", "    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0\n", "    return accuracy, correct_predictions, total_predictions\n", "\n", "# Avaliar modelo\n", "try:\n", "    accuracy, correct, total = evaluate_model_accuracy(model, tokenizer, dataset['test'], num_samples=50)\n", "    \n", "    print(\"\\n📈 RESULTADOS DA AVALIAÇÃO:\")\n", "    print(\"=\" * 40)\n", "    print(f\"Acurácia: {accuracy:.2%}\")\n", "    print(f\"Predições corretas: {correct}/{total}\")\n", "    print(\"=\" * 40)\n", "    \nexcept Exception as e:\n", "    print(f\"❌ Erro durante avaliação: {e}\")\n", "    print(\"💡 A avaliação pode ser executada após o treinamento completo\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Seção 15: Conclusão e Próximos Passos"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎉 Fine-tuning Concluído!\n", "\n", "### ✅ O que foi realizado:\n", "- ✅ Fine-tuning do Gemma 3N 4B Vision para classificação de lesões de pele\n", "- ✅ Treinamento com 10.000 amostras do dataset `ahmed-ai/skin-lesions-classification-dataset`\n", "- ✅ Otimizações de RAM para funcionar com 28GB disponível\n", "- ✅ Fine-tuning das camadas de visão (`finetune_vision_layers=True`)\n", "- ✅ Interface para teste com upload de imagens\n", "- ✅ Modelo salvo em formato padrão e GGUF\n", "\n", "### 🏥 Classes de Lesões Suportadas:\n", "1. **Actinic keratoses** - Queratoses actínicas\n", "2. **Basal cell carcinoma** - Carcinoma basocelular\n", "3. **Benign keratosis-like-lesions** - Lesões benignas tipo queratose\n", "4. **Chickenpox** - Catapora\n", "5. **Cowpox** - <PERSON><PERSON><PERSON><PERSON> bovina\n", "6. **Dermatofibroma** - Dermatofibroma\n", "7. **Healthy** - <PERSON><PERSON>\n", "8. **HFMD** - <PERSON><PERSON><PERSON>-pé-boca\n", "9. **<PERSON><PERSON><PERSON>** - <PERSON><PERSON>\n", "10. **Melanocytic nevi** - <PERSON><PERSON><PERSON>\n", "11. **Melanoma** - Melanoma\n", "12. **Monkeypox** - <PERSON>ar<PERSON><PERSON> dos macacos\n", "13. **Squamous cell carcinoma** - Carcinoma espinocelular\n", "14. **Vascular lesions** - Lesões vasculares\n", "\n", "### 🚀 Próximos Passos:\n", "1. **Treinamento Estendido**: Aumentar épocas e steps para melhor performance\n", "2. **Validação Médica**: Validar resultados com dermatologistas\n", "3. **Augmentação de Dados**: Aplicar técnicas de augmentação para melhorar robustez\n", "4. **Deployment**: Criar API ou aplicação web para uso prático\n", "5. **Avaliação Clínica**: Testar em casos reais com supervisão médica\n", "\n", "### ⚠️ Avisos Importantes:\n", "- Este modelo é para **fins educacionais e de pesquisa**\n", "- **NÃO substitui** consulta médica profissional\n", "- Sempre consulte um **dermatologista** para diagnóstico real\n", "- Resultados podem variar dependendo da qualidade da imagem\n", "\n", "### 📚 Referências:\n", "- Dataset: `ahmed-ai/skin-lesions-classification-dataset`\n", "- Modelo base: `unsloth/gemma-3n-E4B-it`\n", "- Framework: Unsloth para fine-tuning eficiente\n", "\n", "---\n", "\n", "**🎯 Modelo pronto para classificação de lesões de pele!**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}